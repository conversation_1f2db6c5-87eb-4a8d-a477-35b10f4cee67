import { Emitter } from '@kdt310722/utils/event'
import { Pool } from 'undici'
import { parseUrl } from './urls'

export interface SenderOptions {
    pool?: Pool.Options
}

export type SenderEvents = {
    connections: (count: number) => void
}

export class Sender extends Emitter<SenderEvents> {
    public readonly origin: string
    public readonly path: string

    protected readonly pool: Pool

    public constructor(url: string, { pool }: SenderOptions = {}) {
        super()

        const { origin, path } = parseUrl(url)

        this.origin = origin
        this.path = path
        this.pool = this.createPool(origin, pool)
    }

    public get stats() {
        return this.pool.stats
    }

    public async send() {}

    protected createPool(origin: string, options: Pool.Options = {}) {
        const pool = new Pool(origin, options)

        pool.on('connect', () => this.emit('connections', pool.stats.connected))
        pool.on('disconnect', () => this.emit(`connections`, pool.stats.connected))

        return pool
    }
}
